import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/database/translation_database_helper.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/models/offline/bandwidth_usage.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/services/translation_metrics_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// A service for syncing translations between local storage and the server
class TranslationSyncService {
  /// The database helper
  final TranslationDatabaseHelper _dbHelper;

  /// The HTTP client
  final http.Client _client;

  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The translation metrics service
  final TranslationMetricsService _metricsService;

  /// The logging service
  final LoggingService _loggingService;

  /// The connectivity instance
  final Connectivity _connectivity;

  /// Whether the device is online
  bool _isOnline = false;

  /// The current network type
  NetworkType _networkType = NetworkType.unknown;

  /// The connectivity subscription
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// Stream controller for sync events
  final StreamController<String> _syncEventsController =
      StreamController<String>.broadcast();

  /// Stream controller for sync status
  final StreamController<SyncStatus> _syncStatusController =
      StreamController<SyncStatus>.broadcast();

  /// Timer for periodic sync
  Timer? _periodicSyncTimer;

  /// Whether a sync is currently in progress
  bool _isSyncing = false;

  /// The batch size for syncing translations
  static const int _syncBatchSize = 20;

  /// The maximum number of retry attempts
  static const int _maxRetryAttempts = 3;

  /// The backoff delay for retries (in seconds)
  static const List<int> _retryDelays = [5, 15, 30];

  /// Creates a new translation sync service
  TranslationSyncService(
    this._dbHelper,
    this._client,
    this._prefs,
    this._metricsService,
    this._loggingService,
    this._connectivity,
  ) {
    _initConnectivityListener();
    _startPeriodicSync();
  }

  /// Stream of sync events
  Stream<String> get syncEvents => _syncEventsController.stream;

  /// Stream of sync status
  Stream<SyncStatus> get syncStatus => _syncStatusController.stream;

  /// Whether the device is online
  bool get isOnline => _isOnline;

  /// The current network type
  NetworkType get networkType => _networkType;

  /// Whether a sync is currently in progress
  bool get isSyncing => _isSyncing;

  /// Initializes the connectivity listener
  void _initConnectivityListener() {
    // Check initial connectivity
    _connectivity.checkConnectivity().then((result) {
      _updateConnectivityStatus(result);
    });

    // Listen for connectivity changes
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen((result) {
      _updateConnectivityStatus(result);
    });
  }

  /// Updates the connectivity status
  void _updateConnectivityStatus(ConnectivityResult result) {
    final wasOnline = _isOnline;
    _isOnline = result != ConnectivityResult.none;

    // Update network type
    if (result == ConnectivityResult.wifi) {
      _networkType = NetworkType.wifi;
    } else if (result == ConnectivityResult.mobile) {
      _networkType = NetworkType.mobile;
    } else {
      _networkType = NetworkType.unknown;
    }

    // If we just came back online, start a sync
    if (!wasOnline && _isOnline) {
      _syncEventsController.add('connectivity_restored');
      syncTranslations();
    }
  }

  /// Starts the periodic sync timer
  void _startPeriodicSync() {
    // Cancel any existing timer
    _periodicSyncTimer?.cancel();

    // Get the sync interval from preferences (default to 15 minutes)
    final syncIntervalMinutes =
        _prefs.getInt('translation_sync_interval_minutes') ?? 15;

    // Start a new timer
    _periodicSyncTimer = Timer.periodic(
      Duration(minutes: syncIntervalMinutes),
      (_) => _checkAndSync(),
    );
  }

  /// Checks if we should sync and starts a sync if needed
  Future<void> _checkAndSync() async {
    // Don't sync if we're offline
    if (!_isOnline) {
      _loggingService.debug(
          'TranslationSyncService', 'Skipping sync: device is offline');
      return;
    }

    // Don't sync if we're already syncing
    if (_isSyncing) {
      _loggingService.debug(
          'TranslationSyncService', 'Skipping sync: sync already in progress');
      return;
    }

    // Check if we should sync based on network type
    final syncOnWifiOnly = _prefs.getBool('translation_sync_wifi_only') ?? true;
    if (syncOnWifiOnly && _networkType != NetworkType.wifi) {
      _loggingService.debug(
          'TranslationSyncService', 'Skipping sync: not on WiFi');
      return;
    }

    // Start the sync
    await syncTranslations();
  }

  /// Syncs translations with the server
  Future<void> syncTranslations() async {
    if (_isSyncing) {
      _loggingService.debug(
          'TranslationSyncService', 'Sync already in progress');
      return;
    }

    if (!_isOnline) {
      _loggingService.debug(
          'TranslationSyncService', 'Cannot sync: device is offline');
      _syncStatusController.add(SyncStatus.failed);
      return;
    }

    _isSyncing = true;
    _syncStatusController.add(SyncStatus.syncing);
    _syncEventsController.add('sync_started');

    try {
      _loggingService.debug(
          'TranslationSyncService', 'Starting translation sync');

      // Get the next batch of translations to sync
      final batch = await _dbHelper.getNextSyncBatch(_syncBatchSize);

      if (batch.isEmpty) {
        _loggingService.debug(
            'TranslationSyncService', 'No translations to sync');
        _isSyncing = false;
        _syncStatusController.add(SyncStatus.synced);
        _syncEventsController.add('sync_completed');
        return;
      }

      _loggingService.debug(
          'TranslationSyncService', 'Syncing ${batch.length} translations');

      // Sync each translation in the batch
      for (final item in batch) {
        await _syncTranslation(item);
      }

      _loggingService.debug(
          'TranslationSyncService', 'Translation sync completed');
      _syncStatusController.add(SyncStatus.synced);
      _syncEventsController.add('sync_completed');
    } catch (e) {
      _loggingService.error(
          'TranslationSyncService', 'Error syncing translations', e);
      _syncStatusController.add(SyncStatus.failed);
      _syncEventsController.add('sync_failed');
    } finally {
      _isSyncing = false;
    }
  }

  /// Syncs a single translation with the server
  Future<void> _syncTranslation(Map<String, dynamic> item) async {
    final messageId = item['message_id'] as String;
    final groupId = item['group_id'] as String;
    final retryCount = item['retry_count'] as int;

    try {
      // Get the translation from the database
      final translation = await _dbHelper.getGroupMessageTranslation(messageId);

      if (translation == null) {
        _loggingService.warning(
            'TranslationSyncService', 'Translation not found: $messageId');
        await _dbHelper.removeFromSyncQueue(messageId);
        return;
      }

      // TODO: In a real implementation, this would send the translation to the server
      // For now, we'll just simulate a successful sync
      await Future.delayed(const Duration(milliseconds: 500));

      // Update the sync status
      await _dbHelper.updateTranslationSyncStatus(messageId, SyncStatus.synced);

      // Remove from the sync queue
      await _dbHelper.removeFromSyncQueue(messageId);

      _syncEventsController.add('translation_synced_$messageId');
    } catch (e) {
      _loggingService.error(
          'TranslationSyncService', 'Error syncing translation: $messageId', e);

      // Increment retry count
      if (retryCount < _maxRetryAttempts) {
        // Update retry count and schedule for retry
        final db = await _dbHelper.database;
        await db.update(
          'translation_sync_queue',
          {
            'retry_count': retryCount + 1,
            'last_retry': DateTime.now().millisecondsSinceEpoch,
          },
          where: 'message_id = ?',
          whereArgs: [messageId],
        );

        _syncEventsController.add('translation_sync_retry_$messageId');
      } else {
        // Max retries reached, mark as failed
        await _dbHelper.updateTranslationSyncStatus(
            messageId, SyncStatus.failed);
        await _dbHelper.removeFromSyncQueue(messageId);

        _syncEventsController.add('translation_sync_failed_$messageId');
      }
    }
  }

  /// Checks for conflicts with the server
  Future<void> checkForConflicts() async {
    if (!_isOnline) {
      _loggingService.debug('TranslationSyncService',
          'Cannot check for conflicts: device is offline');
      return;
    }

    try {
      _loggingService.debug(
          'TranslationSyncService', 'Checking for translation conflicts');

      // TODO: In a real implementation, this would check with the server for conflicts
      // For now, we'll just simulate no conflicts

      _loggingService.debug('TranslationSyncService', 'No conflicts found');
    } catch (e) {
      _loggingService.error(
          'TranslationSyncService', 'Error checking for conflicts', e);
    }
  }

  /// Resolves a translation conflict
  Future<void> resolveConflict(String messageId, String resolutionType) async {
    try {
      await _dbHelper.resolveTranslationConflict(messageId, resolutionType);
      _syncEventsController.add('conflict_resolved_$messageId');
    } catch (e) {
      _loggingService.error(
          'TranslationSyncService', 'Error resolving conflict: $messageId', e);
    }
  }

  /// Disposes of resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _periodicSyncTimer?.cancel();
    _syncEventsController.close();
    _syncStatusController.close();
  }
}

/// Provider for the translation sync service
final translationSyncServiceProvider = Provider<TranslationSyncService>((ref) {
  final dbHelper = TranslationDatabaseHelper();
  final client = http.Client();
  final prefs = ref.watch(sharedPreferencesProvider);
  final metricsService = ref.watch(translationMetricsServiceProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final connectivity = Connectivity();

  final service = TranslationSyncService(
    dbHelper,
    client,
    prefs,
    metricsService,
    loggingService,
    connectivity,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for the translation sync status
final translationSyncStatusProvider = StreamProvider<SyncStatus>((ref) {
  final syncService = ref.watch(translationSyncServiceProvider);
  return syncService.syncStatus;
});

/// Provider for whether a sync is in progress
final isSyncingProvider = Provider<bool>((ref) {
  final syncService = ref.watch(translationSyncServiceProvider);
  return syncService.isSyncing;
});
