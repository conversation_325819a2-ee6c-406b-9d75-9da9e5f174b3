import 'dart:async';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import 'package:culture_connect/models/analytics/user_journey.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/providers/achievement_provider.dart'
    hide loggingServiceProvider, analyticsServiceProvider;
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/analytics/user_journey_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/mascot_service.dart';

/// Provider for the UXMetricsService
final uxMetricsServiceProvider = Provider<UXMetricsService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final analyticsService = ref.watch(analyticsServiceProvider);
  final achievementService = ref.watch(achievementServiceProvider);
  final mascotService = ref.watch(mascotServiceProvider);
  final journeyService = ref.watch(userJourneyServiceProvider);

  return UXMetricsService(
    prefs: prefs,
    loggingService: loggingService,
    analyticsService: analyticsService,
    achievementService: achievementService,
    mascotService: mascotService,
    journeyService: journeyService,
  );
});

/// UX metric types for categorization
enum UXMetricType {
  taskCompletion,
  errorOccurrence,
  satisfactionRating,
  featureAdoption,
  userBehavior,
  performanceImpact,
  accessibilityUsage,
  searchEffectiveness,
}

extension UXMetricTypeExtension on UXMetricType {
  String get displayName {
    switch (this) {
      case UXMetricType.taskCompletion:
        return 'Task Completion';
      case UXMetricType.errorOccurrence:
        return 'Error Occurrence';
      case UXMetricType.satisfactionRating:
        return 'Satisfaction Rating';
      case UXMetricType.featureAdoption:
        return 'Feature Adoption';
      case UXMetricType.userBehavior:
        return 'User Behavior';
      case UXMetricType.performanceImpact:
        return 'Performance Impact';
      case UXMetricType.accessibilityUsage:
        return 'Accessibility Usage';
      case UXMetricType.searchEffectiveness:
        return 'Search Effectiveness';
    }
  }

  String get value {
    switch (this) {
      case UXMetricType.taskCompletion:
        return 'task_completion';
      case UXMetricType.errorOccurrence:
        return 'error_occurrence';
      case UXMetricType.satisfactionRating:
        return 'satisfaction_rating';
      case UXMetricType.featureAdoption:
        return 'feature_adoption';
      case UXMetricType.userBehavior:
        return 'user_behavior';
      case UXMetricType.performanceImpact:
        return 'performance_impact';
      case UXMetricType.accessibilityUsage:
        return 'accessibility_usage';
      case UXMetricType.searchEffectiveness:
        return 'search_effectiveness';
    }
  }
}

/// UX metrics preferences for privacy control
class UXMetricsPreferences {
  /// Whether UX metrics collection is enabled
  final bool uxMetricsEnabled;

  /// Whether to track task completion metrics
  final bool trackTaskCompletion;

  /// Whether to track error patterns
  final bool trackErrorPatterns;

  /// Whether to collect satisfaction ratings
  final bool collectSatisfactionRatings;

  /// Whether to track feature adoption
  final bool trackFeatureAdoption;

  /// Whether to analyze user behavior patterns
  final bool analyzeUserBehavior;

  /// Data retention period in days
  final int dataRetentionDays;

  /// Whether user has given consent for UX metrics
  final bool hasUserConsent;

  /// Maximum UX metrics to store locally
  final int maxLocalMetrics;

  /// Whether to share anonymized data for improvements
  final bool shareForImprovements;

  const UXMetricsPreferences({
    this.uxMetricsEnabled = true,
    this.trackTaskCompletion = true,
    this.trackErrorPatterns = true,
    this.collectSatisfactionRatings = true,
    this.trackFeatureAdoption = true,
    this.analyzeUserBehavior = true,
    this.dataRetentionDays = 30,
    this.hasUserConsent = false,
    this.maxLocalMetrics = 1000,
    this.shareForImprovements = false,
  });

  UXMetricsPreferences copyWith({
    bool? uxMetricsEnabled,
    bool? trackTaskCompletion,
    bool? trackErrorPatterns,
    bool? collectSatisfactionRatings,
    bool? trackFeatureAdoption,
    bool? analyzeUserBehavior,
    int? dataRetentionDays,
    bool? hasUserConsent,
    int? maxLocalMetrics,
    bool? shareForImprovements,
  }) {
    return UXMetricsPreferences(
      uxMetricsEnabled: uxMetricsEnabled ?? this.uxMetricsEnabled,
      trackTaskCompletion: trackTaskCompletion ?? this.trackTaskCompletion,
      trackErrorPatterns: trackErrorPatterns ?? this.trackErrorPatterns,
      collectSatisfactionRatings:
          collectSatisfactionRatings ?? this.collectSatisfactionRatings,
      trackFeatureAdoption: trackFeatureAdoption ?? this.trackFeatureAdoption,
      analyzeUserBehavior: analyzeUserBehavior ?? this.analyzeUserBehavior,
      dataRetentionDays: dataRetentionDays ?? this.dataRetentionDays,
      hasUserConsent: hasUserConsent ?? this.hasUserConsent,
      maxLocalMetrics: maxLocalMetrics ?? this.maxLocalMetrics,
      shareForImprovements: shareForImprovements ?? this.shareForImprovements,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uxMetricsEnabled': uxMetricsEnabled,
      'trackTaskCompletion': trackTaskCompletion,
      'trackErrorPatterns': trackErrorPatterns,
      'collectSatisfactionRatings': collectSatisfactionRatings,
      'trackFeatureAdoption': trackFeatureAdoption,
      'analyzeUserBehavior': analyzeUserBehavior,
      'dataRetentionDays': dataRetentionDays,
      'hasUserConsent': hasUserConsent,
      'maxLocalMetrics': maxLocalMetrics,
      'shareForImprovements': shareForImprovements,
    };
  }

  factory UXMetricsPreferences.fromJson(Map<String, dynamic> json) {
    return UXMetricsPreferences(
      uxMetricsEnabled: json['uxMetricsEnabled'] as bool? ?? true,
      trackTaskCompletion: json['trackTaskCompletion'] as bool? ?? true,
      trackErrorPatterns: json['trackErrorPatterns'] as bool? ?? true,
      collectSatisfactionRatings:
          json['collectSatisfactionRatings'] as bool? ?? true,
      trackFeatureAdoption: json['trackFeatureAdoption'] as bool? ?? true,
      analyzeUserBehavior: json['analyzeUserBehavior'] as bool? ?? true,
      dataRetentionDays: json['dataRetentionDays'] as int? ?? 30,
      hasUserConsent: json['hasUserConsent'] as bool? ?? false,
      maxLocalMetrics: json['maxLocalMetrics'] as int? ?? 1000,
      shareForImprovements: json['shareForImprovements'] as bool? ?? false,
    );
  }
}

/// UX metric data model
class UXMetric {
  /// Unique identifier for the metric
  final String id;

  /// Type of UX metric
  final UXMetricType type;

  /// Timestamp when metric was recorded
  final DateTime timestamp;

  /// Screen or feature where metric was recorded
  final String screen;

  /// Specific action or event
  final String action;

  /// Metric value (completion rate, rating, etc.)
  final double value;

  /// Additional context data
  final Map<String, dynamic> context;

  /// User session ID for correlation
  final String sessionId;

  /// Journey ID if part of a user journey
  final String? journeyId;

  /// Whether this metric indicates success
  final bool isSuccessful;

  /// Duration for time-based metrics
  final Duration? duration;

  const UXMetric({
    required this.id,
    required this.type,
    required this.timestamp,
    required this.screen,
    required this.action,
    required this.value,
    this.context = const {},
    required this.sessionId,
    this.journeyId,
    this.isSuccessful = true,
    this.duration,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.value,
      'timestamp': timestamp.toIso8601String(),
      'screen': screen,
      'action': action,
      'value': value,
      'context': context,
      'sessionId': sessionId,
      'journeyId': journeyId,
      'isSuccessful': isSuccessful,
      'duration': duration?.inMilliseconds,
    };
  }

  factory UXMetric.fromJson(Map<String, dynamic> json) {
    return UXMetric(
      id: json['id'] as String,
      type: UXMetricType.values.firstWhere(
        (t) => t.value == json['type'],
        orElse: () => UXMetricType.userBehavior,
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
      screen: json['screen'] as String,
      action: json['action'] as String,
      value: (json['value'] as num).toDouble(),
      context: Map<String, dynamic>.from(json['context'] as Map? ?? {}),
      sessionId: json['sessionId'] as String,
      journeyId: json['journeyId'] as String?,
      isSuccessful: json['isSuccessful'] as bool? ?? true,
      duration: json['duration'] != null
          ? Duration(milliseconds: json['duration'] as int)
          : null,
    );
  }
}

/// Service for collecting and analyzing user experience metrics
class UXMetricsService {
  final SharedPreferences _prefs;
  final LoggingService _loggingService;
  final AnalyticsService _analyticsService;
  final AchievementService _achievementService;
  final MascotService _mascotService;
  final UserJourneyService _journeyService;

  /// UUID generator for metric IDs
  static const _uuid = Uuid();

  /// Current session ID
  late final String _sessionId;

  /// Current user preferences
  UXMetricsPreferences _preferences = const UXMetricsPreferences();

  /// Cached UX metrics
  final List<UXMetric> _cachedMetrics = [];

  /// Whether service is initialized
  bool _isInitialized = false;

  /// Storage keys
  static const String _preferencesKey = 'ux_metrics_preferences';
  static const String _metricsKey = 'ux_metrics_cache';

  UXMetricsService({
    required SharedPreferences prefs,
    required LoggingService loggingService,
    required AnalyticsService analyticsService,
    required AchievementService achievementService,
    required MascotService mascotService,
    required UserJourneyService journeyService,
  })  : _prefs = prefs,
        _loggingService = loggingService,
        _analyticsService = analyticsService,
        _achievementService = achievementService,
        _mascotService = mascotService,
        _journeyService = journeyService {
    _sessionId = _uuid.v4();
  }

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadPreferences();
      await _loadCachedMetrics();

      _isInitialized = true;

      _loggingService.info(
        'UXMetricsService',
        'Service initialized',
        {
          'sessionId': _sessionId,
          'metricsEnabled': _preferences.uxMetricsEnabled,
          'hasConsent': _preferences.hasUserConsent,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'UXMetricsService',
        'Failed to initialize service',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Request user consent for UX metrics collection
  Future<bool> requestUserConsent() async {
    try {
      // TODO: Integrate with UX Metrics Consent Dialog UI
      // POST /api/v1/analytics/ux-metrics/consent
      // Headers: Authorization, Content-Type
      // Body: { userId: string, consentGiven: boolean, timestamp: string }
      // Response: { success: boolean, consentId: string }

      // For now, we'll assume consent is given for development
      await updatePreferences(_preferences.copyWith(hasUserConsent: true));

      return true;
    } catch (e) {
      _loggingService.error(
        'UXMetricsService',
        'Failed to request user consent',
        {'error': e.toString()},
      );
      return false;
    }
  }

  /// Update user preferences
  Future<void> updatePreferences(UXMetricsPreferences preferences) async {
    try {
      _preferences = preferences;
      await _prefs.setString(_preferencesKey, jsonEncode(preferences.toJson()));

      _loggingService.info(
        'UXMetricsService',
        'Preferences updated',
        {'preferences': preferences.toJson()},
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'UXMetricsService',
        'Failed to update preferences',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Get current preferences
  UXMetricsPreferences get preferences => _preferences;

  /// Check if UX metrics collection is enabled
  bool get isMetricsEnabled =>
      _preferences.uxMetricsEnabled && _preferences.hasUserConsent;

  /// Track task completion
  Future<void> trackTaskCompletion({
    required String screen,
    required String task,
    required bool isSuccessful,
    Duration? duration,
    Map<String, dynamic>? context,
  }) async {
    if (!isMetricsEnabled || !_preferences.trackTaskCompletion) return;

    try {
      final metric = UXMetric(
        id: _uuid.v4(),
        type: UXMetricType.taskCompletion,
        timestamp: DateTime.now(),
        screen: screen,
        action: task,
        value: isSuccessful ? 1.0 : 0.0,
        context: {
          'task': task,
          'completion_time_ms': duration?.inMilliseconds,
          ...?context,
        },
        sessionId: _sessionId,
        journeyId: _journeyService.getCurrentJourney()?.id,
        isSuccessful: isSuccessful,
        duration: duration,
      );

      await _recordMetric(metric);

      // Track achievement for task completion
      if (isSuccessful) {
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {
            'type': 'task_completed',
            'task': task,
            'screen': screen,
            'duration': duration?.inSeconds ?? 0,
          },
        );
      }

      _loggingService.debug(
        'UXMetricsService',
        'Task completion tracked',
        {
          'task': task,
          'screen': screen,
          'successful': isSuccessful,
          'duration': duration?.inMilliseconds,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'UXMetricsService',
        'Failed to track task completion',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Record error occurrence
  Future<void> recordError({
    required String screen,
    required String action,
    required String errorType,
    String? errorMessage,
    Map<String, dynamic>? context,
  }) async {
    if (!isMetricsEnabled || !_preferences.trackErrorPatterns) return;

    try {
      final metric = UXMetric(
        id: _uuid.v4(),
        type: UXMetricType.errorOccurrence,
        timestamp: DateTime.now(),
        screen: screen,
        action: action,
        value: 1.0, // Error count
        context: {
          'error_type': errorType,
          'error_message': errorMessage,
          'action': action,
          ...?context,
        },
        sessionId: _sessionId,
        journeyId: _journeyService.getCurrentJourney()?.id,
        isSuccessful: false,
      );

      await _recordMetric(metric);

      // Show sympathetic mascot expression for errors
      await _mascotService.onError(
        errorMessage: errorMessage ?? 'An error occurred',
        errorType: errorType,
      );

      _loggingService.debug(
        'UXMetricsService',
        'Error recorded',
        {
          'screen': screen,
          'action': action,
          'errorType': errorType,
          'errorMessage': errorMessage,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'UXMetricsService',
        'Failed to record error',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Collect satisfaction rating
  Future<void> collectSatisfactionRating({
    required String screen,
    required String feature,
    required double rating,
    String? feedback,
    Map<String, dynamic>? context,
  }) async {
    if (!isMetricsEnabled || !_preferences.collectSatisfactionRatings) return;

    try {
      final metric = UXMetric(
        id: _uuid.v4(),
        type: UXMetricType.satisfactionRating,
        timestamp: DateTime.now(),
        screen: screen,
        action: 'rating_submitted',
        value: rating,
        context: {
          'feature': feature,
          'rating': rating,
          'feedback': feedback,
          'rating_scale': '1-5',
          ...?context,
        },
        sessionId: _sessionId,
        journeyId: _journeyService.getCurrentJourney()?.id,
        isSuccessful: rating >= 3.0, // Consider 3+ as positive
      );

      await _recordMetric(metric);

      // Show appropriate mascot expression based on rating
      if (rating >= 4.0) {
        await _mascotService.onBookingSuccessful(
          bookingType: 'Satisfaction',
          bookingId: feature,
        );
      } else if (rating <= 2.0) {
        await _mascotService.onError(
          errorMessage: 'We\'ll work to improve this experience',
          errorType: 'low_satisfaction',
        );
      }

      _loggingService.debug(
        'UXMetricsService',
        'Satisfaction rating collected',
        {
          'screen': screen,
          'feature': feature,
          'rating': rating,
          'feedback': feedback,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'UXMetricsService',
        'Failed to collect satisfaction rating',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Track feature adoption
  Future<void> trackFeatureAdoption({
    required String feature,
    required String screen,
    required String
        adoptionStage, // 'discovered', 'tried', 'adopted', 'mastered'
    Map<String, dynamic>? context,
  }) async {
    if (!isMetricsEnabled || !_preferences.trackFeatureAdoption) return;

    try {
      final adoptionValue = _getAdoptionValue(adoptionStage);

      final metric = UXMetric(
        id: _uuid.v4(),
        type: UXMetricType.featureAdoption,
        timestamp: DateTime.now(),
        screen: screen,
        action: adoptionStage,
        value: adoptionValue,
        context: {
          'feature': feature,
          'adoption_stage': adoptionStage,
          'screen': screen,
          ...?context,
        },
        sessionId: _sessionId,
        journeyId: _journeyService.getCurrentJourney()?.id,
        isSuccessful: adoptionValue > 0.0,
      );

      await _recordMetric(metric);

      // Track achievement for feature adoption milestones
      if (adoptionStage == 'adopted' || adoptionStage == 'mastered') {
        await _achievementService.trackUserAction(
          UserAction.appFeatureUsed,
          metadata: {
            'type': 'feature_adopted',
            'feature': feature,
            'stage': adoptionStage,
            'screen': screen,
          },
        );
      }

      _loggingService.debug(
        'UXMetricsService',
        'Feature adoption tracked',
        {
          'feature': feature,
          'screen': screen,
          'stage': adoptionStage,
          'value': adoptionValue,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'UXMetricsService',
        'Failed to track feature adoption',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Analyze user behavior patterns
  Future<Map<String, dynamic>> analyzeUserBehavior({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final metrics = _getMetricsInPeriod(startDate, endDate);

      final analysis = {
        'totalMetrics': metrics.length,
        'taskCompletionRate': _calculateTaskCompletionRate(metrics),
        'averageSatisfactionRating':
            _calculateAverageSatisfactionRating(metrics),
        'errorFrequency': _calculateErrorFrequency(metrics),
        'featureAdoptionTrends': _analyzeFeatureAdoptionTrends(metrics),
        'mostUsedScreens': _getMostUsedScreens(metrics),
        'commonErrorPatterns': _getCommonErrorPatterns(metrics),
        'satisfactionTrends': _getSatisfactionTrends(metrics),
        'analysisTimestamp': DateTime.now().toIso8601String(),
      };

      _loggingService.info(
        'UXMetricsService',
        'User behavior analysis completed',
        {
          'metricsAnalyzed': metrics.length,
          'taskCompletionRate': analysis['taskCompletionRate'],
          'averageSatisfactionRating': analysis['averageSatisfactionRating'],
        },
      );

      return analysis;
    } catch (e, stackTrace) {
      _loggingService.error(
        'UXMetricsService',
        'Failed to analyze user behavior',
        {'error': e.toString()},
        stackTrace,
      );
      return {};
    }
  }

  /// Generate comprehensive UX report
  Future<Map<String, dynamic>> generateUXReport({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final behaviorAnalysis = await analyzeUserBehavior(
        startDate: startDate,
        endDate: endDate,
      );

      final journeyPatterns = await _journeyService.analyzePatterns(
        startDate: startDate,
        endDate: endDate,
      );

      final report = {
        'reportId': _uuid.v4(),
        'generatedAt': DateTime.now().toIso8601String(),
        'period': {
          'startDate': startDate?.toIso8601String(),
          'endDate': endDate?.toIso8601String(),
        },
        'behaviorAnalysis': behaviorAnalysis,
        'journeyPatterns': journeyPatterns.map((p) => p.toJson()).toList(),
        'recommendations': _generateUXRecommendations(behaviorAnalysis),
        'insights': _generateUXInsights(behaviorAnalysis, journeyPatterns),
        'sessionId': _sessionId,
      };

      // TODO: Integrate with UX Report Generation API
      // POST /api/v1/analytics/ux-metrics/report
      // Headers: Authorization, Content-Type, X-Session-ID
      // Body: {
      //   report: UXReport,
      //   userId: string,
      //   sessionId: string,
      //   reportType: 'comprehensive' | 'summary' | 'detailed',
      //   analysisDepth: 'basic' | 'advanced' | 'expert',
      //   includeRecommendations: boolean,
      //   includeInsights: boolean,
      //   timestamp: string
      // }
      // Response: {
      //   success: boolean,
      //   reportId: string,
      //   processingTime: number,
      //   additionalInsights: [UXInsight],
      //   benchmarkComparison: object,
      //   actionableRecommendations: [string]
      // }

      _loggingService.info(
        'UXMetricsService',
        'UX report generated',
        {
          'reportId': report['reportId'],
          'metricsCount': behaviorAnalysis['totalMetrics'],
          'patternsCount': journeyPatterns.length,
        },
      );

      return report;
    } catch (e, stackTrace) {
      _loggingService.error(
        'UXMetricsService',
        'Failed to generate UX report',
        {'error': e.toString()},
        stackTrace,
      );
      return {};
    }
  }

  /// Export UX metrics data for privacy compliance
  Future<Map<String, dynamic>> exportUXData() async {
    try {
      final data = {
        'sessionId': _sessionId,
        'preferences': _preferences.toJson(),
        'metrics': _cachedMetrics.map((m) => m.toJson()).toList(),
        'exportTimestamp': DateTime.now().toIso8601String(),
      };

      _loggingService.info(
        'UXMetricsService',
        'UX data exported',
        {'metricsCount': _cachedMetrics.length},
      );

      return data;
    } catch (e, stackTrace) {
      _loggingService.error(
        'UXMetricsService',
        'Failed to export UX data',
        {'error': e.toString()},
        stackTrace,
      );
      return {};
    }
  }

  /// Clear all UX metrics data
  Future<void> clearAllMetrics() async {
    try {
      _cachedMetrics.clear();
      await _prefs.remove(_metricsKey);

      _loggingService.info('UXMetricsService', 'All UX metrics data cleared');
    } catch (e, stackTrace) {
      _loggingService.error(
        'UXMetricsService',
        'Failed to clear UX metrics data',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Dispose of the service
  Future<void> dispose() async {
    _loggingService.info('UXMetricsService', 'Service disposed');
  }

  /// Load preferences from storage
  Future<void> _loadPreferences() async {
    try {
      final prefsJson = _prefs.getString(_preferencesKey);
      if (prefsJson != null) {
        final prefsMap = jsonDecode(prefsJson) as Map<String, dynamic>;
        _preferences = UXMetricsPreferences.fromJson(prefsMap);
      }
    } catch (e) {
      _loggingService.warning(
        'UXMetricsService',
        'Failed to load preferences, using defaults',
        {'error': e.toString()},
      );
    }
  }

  /// Load cached metrics from storage
  Future<void> _loadCachedMetrics() async {
    try {
      final metricsJson = _prefs.getString(_metricsKey);
      if (metricsJson != null) {
        final metricsList = jsonDecode(metricsJson) as List<dynamic>;
        _cachedMetrics.clear();

        for (final metricMap in metricsList) {
          try {
            final metric = UXMetric.fromJson(metricMap as Map<String, dynamic>);

            // Check if metric is within retention period
            final retentionDate = DateTime.now()
                .subtract(Duration(days: _preferences.dataRetentionDays));

            if (metric.timestamp.isAfter(retentionDate)) {
              _cachedMetrics.add(metric);
            }
          } catch (e) {
            _loggingService.warning(
              'UXMetricsService',
              'Failed to parse cached metric',
              {'error': e.toString()},
            );
          }
        }
      }
    } catch (e) {
      _loggingService.warning(
        'UXMetricsService',
        'Failed to load cached metrics',
        {'error': e.toString()},
      );
    }
  }

  /// Record a UX metric
  Future<void> _recordMetric(UXMetric metric) async {
    try {
      _cachedMetrics.add(metric);

      // Enforce cache size limit
      if (_cachedMetrics.length > _preferences.maxLocalMetrics) {
        _cachedMetrics.removeAt(0);
      }

      await _saveMetrics();

      // Log to analytics
      await _analyticsService.logEvent(
        name: 'ux_metric_recorded',
        category: AnalyticsCategory.userAction,
        parameters: {
          'metric_type': metric.type.value,
          'screen': metric.screen,
          'action': metric.action,
          'value': metric.value,
          'successful': metric.isSuccessful,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'UXMetricsService',
        'Failed to record metric',
        {'error': e.toString()},
        stackTrace,
      );
    }
  }

  /// Save metrics to storage
  Future<void> _saveMetrics() async {
    try {
      final metricsData = _cachedMetrics.map((m) => m.toJson()).toList();
      await _prefs.setString(_metricsKey, jsonEncode(metricsData));
    } catch (e) {
      _loggingServicearning(
        'UXMetricsService',
        'Failed to save metrics',
        {'error': e.toString()},
      );
    }
  }

  /// Get adoption value for stage
  double _getAdoptionValue(String stage) {
    switch (stage.toLowerCase()) {
      case 'discovered':
        return 0.25;
      case 'tried':
        return 0.5;
      case 'adopted':
        return 0.75;
      case 'mastered':
        return 1.0;
      default:
        return 0.0;
    }
  }

  /// Get metrics within a specific time period
  List<UXMetric> _getMetricsInPeriod(DateTime? startDate, DateTime? endDate) {
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    return _cachedMetrics
        here((metric) =>
            metric.timestamp.isAfter(start) && metric.timestamp.isBefore(end))
        .toList();
  }

  /// Calculate task completion rate
  double _calculateTaskCompletionRate(List<UXMetric> metrics) {
    final taskMetrics =
        metricshere((m) => m.type == UXMetricType.taskCompletion).toList();
    if (taskMetrics.isEmpty) return 0.0;

    final successfulTasks = taskMetricshere((m) => m.isSuccessful).length;
    return successfulTasks / taskMetrics.length;
  }

  /// Calculate average satisfaction rating
  double _calculateAverageSatisfactionRating(List<UXMetric> metrics) {
    final ratingMetrics = metrics
        here((m) => m.type == UXMetricType.satisfactionRating)
        .toList();
    if (ratingMetrics.isEmpty) return 0.0;

    final totalRating =
        ratingMetrics.map((m) => m.value)educe((a, b) => a + b);
    return totalRating / ratingMetrics.length;
  }

  /// Calculate error frequency
  double _calculateErrorFrequency(List<UXMetric> metrics) {
    final errorMetrics =
        metricshere((m) => m.type == UXMetricType.errorOccurrence).toList();
    if (metrics.isEmpty) return 0.0;

    return errorMetrics.length / metrics.length;
  }

  /// Analyze feature adoption trends
  Map<String, dynamic> _analyzeFeatureAdoptionTrends(List<UXMetric> metrics) {
    final adoptionMetrics =
        metricshere((m) => m.type == UXMetricType.featureAdoption).toList();
    final featureTrends = <String, Map<String, int>>{};

    for (final metric in adoptionMetrics) {
      final feature = metric.context['feature'] as String? ?? 'unknown';
      final stage = metric.context['adoption_stage'] as String? ?? 'unknown';

      featureTrends.putIfAbsent(feature, () => {});
      featureTrends[feature]![stage] =
          (featureTrends[feature]![stage] ?? 0) + 1;
    }

    return featureTrends;
  }

  /// Get most used screens
  List<Map<String, dynamic>> _getMostUsedScreens(List<UXMetric> metrics) {
    final screenCounts = <String, int>{};

    for (final metric in metrics) {
      screenCounts[metric.screen] = (screenCounts[metric.screen] ?? 0) + 1;
    }

    final sortedScreens = screenCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedScreens
        .take(10)
        .map((entry) => {
              'screen': entry.key,
              'count': entry.value,
            })
        .toList();
  }

  /// Get common error patterns
  List<Map<String, dynamic>> _getCommonErrorPatterns(List<UXMetric> metrics) {
    final errorMetrics =
        metricshere((m) => m.type == UXMetricType.errorOccurrence).toList();
    final errorPatterns = <String, int>{};

    for (final metric in errorMetrics) {
      final errorType = metric.context['error_type'] as String? ?? 'unknown';
      errorPatterns[errorType] = (errorPatterns[errorType] ?? 0) + 1;
    }

    final sortedErrors = errorPatterns.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedErrors
        .take(5)
        .map((entry) => {
              'error_type': entry.key,
              'count': entry.value,
            })
        .toList();
  }

  /// Get satisfaction trends
  Map<String, dynamic> _getSatisfactionTrends(List<UXMetric> metrics) {
    final ratingMetrics = metrics
        here((m) => m.type == UXMetricType.satisfactionRating)
        .toList();
    final trends = <String, List<double>>{};

    for (final metric in ratingMetrics) {
      final feature = metric.context['feature'] as String? ?? 'unknown';
      trends.putIfAbsent(feature, () => []);
      trends[feature]!.add(metric.value);
    }

    final trendAnalysis = <String, dynamic>{};
    for (final entry in trends.entries) {
      final ratings = entry.value;
      if (ratings.isNotEmpty) {
        trendAnalysis[entry.key] = {
          'average': ratingseduce((a, b) => a + b) / ratings.length,
          'count': ratings.length,
          'trend': ratings.length > 1 ? _calculateTrend(ratings) : 'stable',
        };
      }
    }

    return trendAnalysis;
  }

  /// Calculate trend direction
  String _calculateTrend(List<double> values) {
    if (values.length < 2) return 'stable';

    final firstHalf = values.take(values.length ~/ 2).toList();
    final secondHalf = values.skip(values.length ~/ 2).toList();

    final firstAvg = firstHalfeduce((a, b) => a + b) / firstHalf.length;
    final secondAvg = secondHalfeduce((a, b) => a + b) / secondHalf.length;

    if (secondAvg > firstAvg + 0.2) return 'improving';
    if (secondAvg < firstAvg - 0.2) return 'declining';
    return 'stable';
  }

  /// Generate UX recommendations
  List<String> _generateUXRecommendations(Map<String, dynamic> analysis) {
    final recommendations = <String>[];

    final taskCompletionRate = analysis['taskCompletionRate'] as double? ?? 0.0;
    final averageSatisfactionRating =
        analysis['averageSatisfactionRating'] as double? ?? 0.0;
    final errorFrequency = analysis['errorFrequency'] as double? ?? 0.0;

    if (taskCompletionRate < 0.7) {
      recommendations
          .add('Improve task completion rate by simplifying user flows');
    }

    if (averageSatisfactionRating < 3.5) {
      recommendations
          .add('Focus on improving user satisfaction through better UX design');
    }

    if (errorFrequency > 0.1) {
      recommendations.add(
          'Reduce error frequency by improving error handling and validation');
    }

    if (recommendations.isEmpty) {
      recommendations.add(
          'Continue monitoring UX metrics to maintain high user satisfaction');
    }

    return recommendations;
  }

  /// Generate UX insights
  List<String> _generateUXInsights(
      Map<String, dynamic> analysis, List<JourneyPattern> patterns) {
    final insights = <String>[];

    final taskCompletionRate = analysis['taskCompletionRate'] as double? ?? 0.0;
    final averageSatisfactionRating =
        analysis['averageSatisfactionRating'] as double? ?? 0.0;

    if (taskCompletionRate > 0.8) {
      insights.add('High task completion rate indicates effective user flows');
    }

    if (averageSatisfactionRating > 4.0) {
      insights.add('Excellent user satisfaction scores show strong UX design');
    }

    if (patterns.isNotEmpty) {
      final successfulPatterns =
          patternshere((p) => p.successRate > 0.8).length;
      if (successfulPatterns > patterns.length * 0.7) {
        insights.add('Most user journey patterns show high success rates');
      }
    }

    if (insights.isEmpty) {
      insights
          .add('UX metrics show room for improvement across multiple areas');
    }

    return insights;
  }
}
