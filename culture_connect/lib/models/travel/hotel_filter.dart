import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'dart:math';

/// Sort options for hotels
enum HotelSortOption {
  /// Sort by price (low to high)
  priceLowToHigh,

  /// Sort by price (high to low)
  priceHighToLow,

  /// Sort by rating (high to low)
  rating,

  /// Sort by popularity (review count)
  popularity,

  /// Sort by distance from city center
  distanceFromCenter,
}

/// Extension for hotel sort options
extension HotelSortOptionExtension on HotelSortOption {
  /// Get the display name for the sort option
  String get displayName {
    switch (this) {
      case HotelSortOption.priceLowToHigh:
        return 'Price: Low to High';
      case HotelSortOption.priceHighToLow:
        return 'Price: High to Low';
      case HotelSortOption.rating:
        return 'Rating';
      case HotelSortOption.popularity:
        return 'Popularity';
      case HotelSortOption.distanceFromCenter:
        return 'Distance from Center';
    }
  }

  /// Get the icon for the sort option
  IconData get icon {
    switch (this) {
      case HotelSortOption.priceLowToHigh:
        return Icons.arrow_upward;
      case HotelSortOption.priceHighToLow:
        return Icons.arrow_downward;
      case HotelSortOption.rating:
        return Icons.star;
      case HotelSortOption.popularity:
        return Icons.trending_up;
      case HotelSortOption.distanceFromCenter:
        return Icons.location_on;
    }
  }
}

/// Filter options for hotels
class HotelFilter {
  /// Selected star ratings
  final List<HotelStarRating>? starRatings;

  /// Price range (min, max)
  final RangeValues? priceRange;

  /// Rating filter (minimum rating)
  final double? minRating;

  /// Check-in date
  final DateTime? checkInDate;

  /// Check-out date
  final DateTime? checkOutDate;

  /// Location search query
  final String? locationQuery;

  /// Current user location
  final LatLng? userLocation;

  /// Maximum distance from user location (in km)
  final double? maxDistance;

  /// Minimum number of guests
  final int? minGuests;

  /// Amenities to filter by
  final Map<String, bool>? amenities;

  /// Sort option
  final HotelSortOption sortOption;

  /// Creates a new hotel filter
  const HotelFilter({
    this.starRatings,
    this.priceRange,
    this.minRating,
    this.checkInDate,
    this.checkOutDate,
    this.locationQuery,
    this.userLocation,
    this.maxDistance,
    this.minGuests,
    this.amenities,
    this.sortOption = HotelSortOption.rating,
  });

  /// Create a copy with some fields replaced
  HotelFilter copyWith({
    List<HotelStarRating>? starRatings,
    RangeValues? priceRange,
    double? minRating,
    DateTime? checkInDate,
    DateTime? checkOutDate,
    String? locationQuery,
    LatLng? userLocation,
    double? maxDistance,
    int? minGuests,
    Map<String, bool>? amenities,
    HotelSortOption? sortOption,
  }) {
    return HotelFilter(
      starRatings: starRatings ?? this.starRatings,
      priceRange: priceRange ?? this.priceRange,
      minRating: minRating ?? this.minRating,
      checkInDate: checkInDate ?? this.checkInDate,
      checkOutDate: checkOutDate ?? this.checkOutDate,
      locationQuery: locationQuery ?? this.locationQuery,
      userLocation: userLocation ?? this.userLocation,
      maxDistance: maxDistance ?? this.maxDistance,
      minGuests: minGuests ?? this.minGuests,
      amenities: amenities ?? this.amenities,
      sortOption: sortOption ?? this.sortOption,
    );
  }

  /// Reset all filters
  HotelFilter reset() {
    return const HotelFilter(
      sortOption: HotelSortOption.rating,
    );
  }

  /// Apply the filter to a list of hotels
  List<Hotel> apply(List<Hotel> hotels) {
    var filtered = List<Hotel>.from(hotels);

    // Filter by star ratings
    if (starRatings != null && starRatings!.isNotEmpty) {
      filtered = filtered
          .where((hotel) => starRatings!.contains(hotel.starRating))
          .toList();
    }

    // Filter by price range
    if (priceRange != null) {
      filtered = filtered
          .where((hotel) =>
              hotel.price >= priceRange!.start &&
              hotel.price <= priceRange!.end)
          .toList();
    }

    // Filter by rating
    if (minRating != null) {
      filtered = filtered.where((hotel) => hotel.rating >= minRating!).toList();
    }

    // Filter by location query
    if (locationQuery != null && locationQuery!.isNotEmpty) {
      filtered = filtered
          .where((hotel) => hotel.location
              .toLowerCase()
              .contains(locationQuery!.toLowerCase()))
          .toList();
    }

    // Filter by distance from user location
    if (userLocation != null && maxDistance != null) {
      filtered = filtered.where((hotel) {
        final distance = _calculateDistance(
          userLocation!.latitude,
          userLocation!.longitude,
          hotel.coordinates.latitude,
          hotel.coordinates.longitude,
        );
        return distance <= maxDistance!;
      }).toList();
    }

    // Filter by minimum guests
    if (minGuests != null) {
      filtered = filtered
          .where((hotel) =>
              hotel.rooms.any((room) => room.maxGuests >= minGuests!))
          .toList();
    }

    // Filter by amenities
    if (amenities != null && amenities!.isNotEmpty) {
      filtered = filtered.where((hotel) {
        if (amenities!['restaurant'] == true && !hotel.hasRestaurant) {
          return false;
        }
        if (amenities!['bar'] == true && !hotel.hasBar) {
          return false;
        }
        if (amenities!['pool'] == true && !hotel.hasPool) {
          return false;
        }
        if (amenities!['spa'] == true && !hotel.hasSpa) {
          return false;
        }
        if (amenities!['gym'] == true && !hotel.hasGym) {
          return false;
        }
        if (amenities!['freeWifi'] == true && !hotel.hasFreeWifi) {
          return false;
        }
        if (amenities!['freeParking'] == true && !hotel.hasFreeParking) {
          return false;
        }
        if (amenities!['roomService'] == true && !hotel.hasRoomService) {
          return false;
        }
        if (amenities!['businessCenter'] == true && !hotel.hasBusinessCenter) {
          return false;
        }
        if (amenities!['kidsClub'] == true && !hotel.hasKidsClub) {
          return false;
        }
        return true;
      }).toList();
    }

    // Sort the results
    switch (sortOption) {
      case HotelSortOption.priceLowToHigh:
        filtered.sort((a, b) => a.price.compareTo(b.price));
        break;
      case HotelSortOption.priceHighToLow:
        filtered.sort((a, b) => b.price.compareTo(a.price));
        break;
      case HotelSortOption.rating:
        filtered.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case HotelSortOption.popularity:
        filtered.sort((a, b) => b.reviewCount.compareTo(a.reviewCount));
        break;
      case HotelSortOption.distanceFromCenter:
        filtered.sort((a, b) =>
            a.distanceFromCityCenter.compareTo(b.distanceFromCityCenter));
        break;
    }

    return filtered;
  }

  /// Calculate distance between two coordinates using the Haversine formula
  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const p = 0.017453292519943295; // Math.PI / 180
    const c = 0.5 - 0.5 * 0.9996 + 0.0006 * 0.5; // cos(0.5 - 0.5)
    final a = 0.5 -
        0.5 * cos((lat2 - lat1) * p) +
        c * cos(lat1 * p) * cos(lat2 * p) * (1 - cos((lon2 - lon1) * p));
    return 12742 * sqrt(a); // 2 * R; R = 6371 km
  }
}
