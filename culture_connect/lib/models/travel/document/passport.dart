import 'package:culture_connect/models/travel/document/travel_document_base.dart';
import 'package:intl/intl.dart';

/// A model representing a passport
class Passport extends TravelDocument {
  /// Nationality of the passport holder
  final String nationality;

  /// Country code of the passport
  final String countryCode;

  /// Place of birth
  final String placeOfBirth;

  /// Date of birth
  final DateTime dateOfBirth;

  /// Gender
  final String gender;

  /// MRZ (Machine Readable Zone) line 1
  final String? mrzLine1;

  /// MRZ (Machine Readable Zone) line 2
  final String? mrzLine2;

  /// Creates a new passport
  const Passport({
    required super.id,
    required super.userId,
    required super.name,
    required super.documentNumber,
    required super.issuedBy,
    required super.issuedDate,
    required super.expiryDate,
    required super.status,
    super.notes,
    required super.documentImageUrls,
    required super.createdAt,
    required super.updatedAt,
    required this.nationality,
    required this.countryCode,
    required this.placeOfBirth,
    required this.dateOfBirth,
    required this.gender,
    this.mrzLine1,
    this.mrzLine2,
  }) : super(type: TravelDocumentType.passport);

  /// Create a passport from a JSON map
  factory Passport.fromJson(Map<String, dynamic> json) {
    return Passport(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      documentNumber: json['documentNumber'] as String,
      issuedBy: json['issuedBy'] as String,
      issuedDate: DateTime.parse(json['issuedDate'] as String),
      expiryDate: DateTime.parse(json['expiryDate'] as String),
      status: TravelDocumentStatus.values.firstWhere(
        (e) => e.toString() == 'TravelDocumentStatus.${json['status']}',
        orElse: () => TravelDocumentStatus.valid,
      ),
      notes: json['notes'] as String?,
      documentImageUrls: (json['documentImageUrls'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      nationality: json['nationality'] as String,
      countryCode: json['countryCode'] as String,
      placeOfBirth: json['placeOfBirth'] as String,
      dateOfBirth: DateTime.parse(json['dateOfBirth'] as String),
      gender: json['gender'] as String,
      mrzLine1: json['mrzLine1'] as String?,
      mrzLine2: json['mrzLine2'] as String?,
    );
  }

  /// Get the formatted date of birth
  String get formattedDateOfBirth {
    return DateFormat('MMM dd, yyyy').format(dateOfBirth);
  }

  /// Get the age of the passport holder
  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month ||
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.toString().split('.').last,
      'name': name,
      'documentNumber': documentNumber,
      'issuedBy': issuedBy,
      'issuedDate': issuedDate.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'status': status.toString().split('.').last,
      'notes': notes,
      'documentImageUrls': documentImageUrls,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'nationality': nationality,
      'countryCode': countryCode,
      'placeOfBirth': placeOfBirth,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'gender': gender,
      'mrzLine1': mrzLine1,
      'mrzLine2': mrzLine2,
    };
  }

  @override
  Passport copyWith({
    String? id,
    String? userId,
    TravelDocumentType? type,
    String? name,
    String? documentNumber,
    String? issuedBy,
    DateTime? issuedDate,
    DateTime? expiryDate,
    TravelDocumentStatus? status,
    String? notes,
    List<String>? documentImageUrls,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? nationality,
    String? countryCode,
    String? placeOfBirth,
    DateTime? dateOfBirth,
    String? gender,
    String? mrzLine1,
    String? mrzLine2,
  }) {
    return Passport(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      documentNumber: documentNumber ?? this.documentNumber,
      issuedBy: issuedBy ?? this.issuedBy,
      issuedDate: issuedDate ?? this.issuedDate,
      expiryDate: expiryDate ?? this.expiryDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      documentImageUrls: documentImageUrls ?? this.documentImageUrls,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      nationality: nationality ?? this.nationality,
      countryCode: countryCode ?? this.countryCode,
      placeOfBirth: placeOfBirth ?? this.placeOfBirth,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      mrzLine1: mrzLine1 ?? this.mrzLine1,
      mrzLine2: mrzLine2 ?? this.mrzLine2,
    );
  }
}
