import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Enum representing the different types of document reminders
enum DocumentReminderType {
  /// Expiry reminder
  expiry,

  /// Renewal reminder
  renewal,

  /// Application reminder
  application,

  /// Verification reminder
  verification,

  /// Custom reminder
  custom,
}

/// Extension for document reminder types
extension DocumentReminderTypeExtension on DocumentReminderType {
  /// Get the display name for the document reminder type
  String get displayName {
    switch (this) {
      case DocumentReminderType.expiry:
        return 'Expiry Reminder';
      case DocumentReminderType.renewal:
        return 'Renewal Reminder';
      case DocumentReminderType.application:
        return 'Application Reminder';
      case DocumentReminderType.verification:
        return 'Verification Reminder';
      case DocumentReminderType.custom:
        return 'Custom Reminder';
    }
  }

  /// Get the icon for the document reminder type
  IconData get icon {
    switch (this) {
      case DocumentReminderType.expiry:
        return Icons.timer;
      case DocumentReminderType.renewal:
        return Icons.autorenew;
      case DocumentReminderType.application:
        return Icons.assignment;
      case DocumentReminderType.verification:
        return Icons.verified;
      case DocumentReminderType.custom:
        return Icons.notifications;
    }
  }

  /// Get the color for the document reminder type
  Color get color {
    switch (this) {
      case DocumentReminderType.expiry:
        return Colors.red;
      case DocumentReminderType.renewal:
        return Colors.orange;
      case DocumentReminderType.application:
        return Colors.blue;
      case DocumentReminderType.verification:
        return Colors.green;
      case DocumentReminderType.custom:
        return Colors.purple;
    }
  }
}

/// A model representing a document reminder
class DocumentReminder {
  /// Unique identifier for the document reminder
  final String id;

  /// ID of the user who owns the reminder
  final String userId;

  /// ID of the document associated with the reminder
  final String documentId;

  /// Type of document reminder
  final DocumentReminderType type;

  /// Title of the reminder
  final String title;

  /// Message of the reminder
  final String message;

  /// Date when the reminder should be triggered
  final DateTime reminderDate;

  /// Whether the reminder has been read
  final bool isRead;

  /// Whether the reminder has been dismissed
  final bool isDismissed;

  /// Whether the reminder is recurring
  final bool isRecurring;

  /// Recurrence interval in days (if recurring)
  final int? recurrenceIntervalDays;

  /// When the reminder was created
  final DateTime createdAt;

  /// When the reminder was last updated
  final DateTime updatedAt;

  /// Creates a new document reminder
  const DocumentReminder({
    required this.id,
    required this.userId,
    required this.documentId,
    required this.type,
    required this.title,
    required this.message,
    required this.reminderDate,
    required this.isRead,
    required this.isDismissed,
    required this.isRecurring,
    this.recurrenceIntervalDays,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create a document reminder from a JSON map
  factory DocumentReminder.fromJson(Map<String, dynamic> json) {
    return DocumentReminder(
      id: json['id'] as String,
      userId: json['userId'] as String,
      documentId: json['documentId'] as String,
      type: DocumentReminderType.values.firstWhere(
        (e) => e.toString() == 'DocumentReminderType.${json['type']}',
        orElse: () => DocumentReminderType.expiry,
      ),
      title: json['title'] as String,
      message: json['message'] as String,
      reminderDate: DateTime.parse(json['reminderDate'] as String),
      isRead: json['isRead'] as bool,
      isDismissed: json['isDismissed'] as bool,
      isRecurring: json['isRecurring'] as bool,
      recurrenceIntervalDays: json['recurrenceIntervalDays'] as int?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert the document reminder to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'documentId': documentId,
      'type': type.toString().split('.').last,
      'title': title,
      'message': message,
      'reminderDate': reminderDate.toIso8601String(),
      'isRead': isRead,
      'isDismissed': isDismissed,
      'isRecurring': isRecurring,
      'recurrenceIntervalDays': recurrenceIntervalDays,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Get the formatted reminder date
  String get formattedReminderDate {
    return DateFormat('MMM dd, yyyy').format(reminderDate);
  }

  /// Check if the reminder is due
  bool get isDue {
    return reminderDate.isBefore(DateTime.now()) && !isRead && !isDismissed;
  }

  /// Check if the reminder is upcoming
  bool get isUpcoming {
    return reminderDate.isAfter(DateTime.now()) && !isDismissed;
  }

  /// Get the days until the reminder is due
  int get daysUntilDue {
    return reminderDate.difference(DateTime.now()).inDays;
  }

  /// Get the formatted days until due
  String get formattedDaysUntilDue {
    final days = daysUntilDue;
    if (days < 0) {
      return 'Overdue by ${-days} ${-days == 1 ? 'day' : 'days'}';
    } else if (days == 0) {
      return 'Due today';
    } else {
      return 'Due in $days ${days == 1 ? 'day' : 'days'}';
    }
  }

  /// Create a copy of this document reminder with the given fields replaced with new values
  DocumentReminder copyWith({
    String? id,
    String? userId,
    String? documentId,
    DocumentReminderType? type,
    String? title,
    String? message,
    DateTime? reminderDate,
    bool? isRead,
    bool? isDismissed,
    bool? isRecurring,
    int? recurrenceIntervalDays,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DocumentReminder(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      documentId: documentId ?? this.documentId,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      reminderDate: reminderDate ?? this.reminderDate,
      isRead: isRead ?? this.isRead,
      isDismissed: isDismissed ?? this.isDismissed,
      isRecurring: isRecurring ?? this.isRecurring,
      recurrenceIntervalDays:
          recurrenceIntervalDays ?? this.recurrenceIntervalDays,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
