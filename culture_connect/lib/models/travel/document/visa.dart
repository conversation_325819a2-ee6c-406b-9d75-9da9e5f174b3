import 'package:culture_connect/models/travel/document/travel_document_base.dart';

/// Enum representing the different types of visas
enum VisaType {
  /// Tourist visa
  tourist,

  /// Business visa
  business,

  /// Student visa
  student,

  /// Work visa
  work,

  /// Transit visa
  transit,

  /// Diplomatic visa
  diplomatic,

  /// Electronic visa (e-Visa)
  electronic,

  /// Visa on arrival
  onArrival,

  /// Other visa type
  other,
}

/// Extension for visa types
extension VisaTypeExtension on VisaType {
  /// Get the display name for the visa type
  String get displayName {
    switch (this) {
      case VisaType.tourist:
        return 'Tourist Visa';
      case VisaType.business:
        return 'Business Visa';
      case VisaType.student:
        return 'Student Visa';
      case VisaType.work:
        return 'Work Visa';
      case VisaType.transit:
        return 'Transit Visa';
      case VisaType.diplomatic:
        return 'Diplomatic Visa';
      case VisaType.electronic:
        return 'Electronic Visa (e-Visa)';
      case VisaType.onArrival:
        return 'Visa on Arrival';
      case VisaType.other:
        return 'Other Visa';
    }
  }
}

/// Enum representing the different entry types for visas
enum VisaEntryType {
  /// Single entry
  single,

  /// Double entry
  double,

  /// Multiple entry
  multiple,
}

/// Extension for visa entry types
extension VisaEntryTypeExtension on VisaEntryType {
  /// Get the display name for the visa entry type
  String get displayName {
    switch (this) {
      case VisaEntryType.single:
        return 'Single Entry';
      case VisaEntryType.double:
        return 'Double Entry';
      case VisaEntryType.multiple:
        return 'Multiple Entry';
    }
  }
}

/// A model representing a visa
class Visa extends TravelDocument {
  /// Type of visa
  final VisaType visaType;

  /// Entry type
  final VisaEntryType entryType;

  /// Country of issue
  final String countryOfIssue;

  /// Country for which the visa is valid
  final String countryValidFor;

  /// Maximum duration of stay in days
  final int maxStayDuration;

  /// Number of entries allowed
  final int? numberOfEntries;

  /// Processing time in days
  final int? processingTime;

  /// Application reference number
  final String? applicationReference;

  /// Creates a new visa
  const Visa({
    required super.id,
    required super.userId,
    required super.name,
    required super.documentNumber,
    required super.issuedBy,
    required super.issuedDate,
    required super.expiryDate,
    required super.status,
    super.notes,
    required super.documentImageUrls,
    required super.createdAt,
    required super.updatedAt,
    required this.visaType,
    required this.entryType,
    required this.countryOfIssue,
    required this.countryValidFor,
    required this.maxStayDuration,
    this.numberOfEntries,
    this.processingTime,
    this.applicationReference,
  }) : super(type: TravelDocumentType.visa);

  /// Create a visa from a JSON map
  factory Visa.fromJson(Map<String, dynamic> json) {
    return Visa(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      documentNumber: json['documentNumber'] as String,
      issuedBy: json['issuedBy'] as String,
      issuedDate: DateTime.parse(json['issuedDate'] as String),
      expiryDate: DateTime.parse(json['expiryDate'] as String),
      status: TravelDocumentStatus.values.firstWhere(
        (e) => e.toString() == 'TravelDocumentStatus.${json['status']}',
        orElse: () => TravelDocumentStatus.valid,
      ),
      notes: json['notes'] as String?,
      documentImageUrls: (json['documentImageUrls'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      visaType: VisaType.values.firstWhere(
        (e) => e.toString() == 'VisaType.${json['visaType']}',
        orElse: () => VisaType.tourist,
      ),
      entryType: VisaEntryType.values.firstWhere(
        (e) => e.toString() == 'VisaEntryType.${json['entryType']}',
        orElse: () => VisaEntryType.single,
      ),
      countryOfIssue: json['countryOfIssue'] as String,
      countryValidFor: json['countryValidFor'] as String,
      maxStayDuration: json['maxStayDuration'] as int,
      numberOfEntries: json['numberOfEntries'] as int?,
      processingTime: json['processingTime'] as int?,
      applicationReference: json['applicationReference'] as String?,
    );
  }

  /// Get the formatted maximum stay duration
  String get formattedMaxStayDuration {
    if (maxStayDuration >= 365) {
      final years = (maxStayDuration / 365).floor();
      final days = maxStayDuration % 365;
      if (days == 0) {
        return '$years ${years == 1 ? 'year' : 'years'}';
      } else {
        return '$years ${years == 1 ? 'year' : 'years'} and $days ${days == 1 ? 'day' : 'days'}';
      }
    } else if (maxStayDuration >= 30) {
      final months = (maxStayDuration / 30).floor();
      final days = maxStayDuration % 30;
      if (days == 0) {
        return '$months ${months == 1 ? 'month' : 'months'}';
      } else {
        return '$months ${months == 1 ? 'month' : 'months'} and $days ${days == 1 ? 'day' : 'days'}';
      }
    } else {
      return '$maxStayDuration ${maxStayDuration == 1 ? 'day' : 'days'}';
    }
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.toString().split('.').last,
      'name': name,
      'documentNumber': documentNumber,
      'issuedBy': issuedBy,
      'issuedDate': issuedDate.toIso8601String(),
      'expiryDate': expiryDate.toIso8601String(),
      'status': status.toString().split('.').last,
      'notes': notes,
      'documentImageUrls': documentImageUrls,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'visaType': visaType.toString().split('.').last,
      'entryType': entryType.toString().split('.').last,
      'countryOfIssue': countryOfIssue,
      'countryValidFor': countryValidFor,
      'maxStayDuration': maxStayDuration,
      'numberOfEntries': numberOfEntries,
      'processingTime': processingTime,
      'applicationReference': applicationReference,
    };
  }

  @override
  Visa copyWith({
    String? id,
    String? userId,
    TravelDocumentType? type,
    String? name,
    String? documentNumber,
    String? issuedBy,
    DateTime? issuedDate,
    DateTime? expiryDate,
    TravelDocumentStatus? status,
    String? notes,
    List<String>? documentImageUrls,
    DateTime? createdAt,
    DateTime? updatedAt,
    VisaType? visaType,
    VisaEntryType? entryType,
    String? countryOfIssue,
    String? countryValidFor,
    int? maxStayDuration,
    int? numberOfEntries,
    int? processingTime,
    String? applicationReference,
  }) {
    return Visa(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      documentNumber: documentNumber ?? this.documentNumber,
      issuedBy: issuedBy ?? this.issuedBy,
      issuedDate: issuedDate ?? this.issuedDate,
      expiryDate: expiryDate ?? this.expiryDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      documentImageUrls: documentImageUrls ?? this.documentImageUrls,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      visaType: visaType ?? this.visaType,
      entryType: entryType ?? this.entryType,
      countryOfIssue: countryOfIssue ?? this.countryOfIssue,
      countryValidFor: countryValidFor ?? this.countryValidFor,
      maxStayDuration: maxStayDuration ?? this.maxStayDuration,
      numberOfEntries: numberOfEntries ?? this.numberOfEntries,
      processingTime: processingTime ?? this.processingTime,
      applicationReference: applicationReference ?? this.applicationReference,
    );
  }
}
