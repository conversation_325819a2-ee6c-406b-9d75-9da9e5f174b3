import 'package:culture_connect/models/travel/travel.dart';

/// A model representing a price comparison source
class PriceSource {
  /// Unique identifier for the price source
  final String id;

  /// Name of the price source
  final String name;

  /// Logo URL of the price source
  final String logoUrl;

  /// Website URL of the price source
  final String websiteUrl;

  /// Rating of the price source (0-5)
  final double rating;

  /// Number of reviews for the price source
  final int reviewCount;

  /// Whether the price source is verified
  final bool isVerified;

  /// Whether the price source is a partner
  final bool isPartner;

  /// Creates a new price source
  const PriceSource({
    required this.id,
    required this.name,
    required this.logoUrl,
    required this.websiteUrl,
    required this.rating,
    required this.reviewCount,
    this.isVerified = false,
    this.isPartner = false,
  });

  /// Creates a copy with some fields replaced
  PriceSource copyWith({
    String? id,
    String? name,
    String? logoUrl,
    String? websiteUrl,
    double? rating,
    int? reviewCount,
    bool? isVerified,
    bool? isPartner,
  }) {
    return PriceSource(
      id: id ?? this.id,
      name: name ?? this.name,
      logoUrl: logoUrl ?? this.logoUrl,
      websiteUrl: websiteUrl ?? this.websiteUrl,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isVerified: isVerified ?? this.isVerified,
      isPartner: isPartner ?? this.isPartner,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'logoUrl': logoUrl,
      'websiteUrl': websiteUrl,
      'rating': rating,
      'reviewCount': reviewCount,
      'isVerified': isVerified,
      'isPartner': isPartner,
    };
  }

  /// Creates from JSON
  factory PriceSource.fromJson(Map<String, dynamic> json) {
    return PriceSource(
      id: json['id'] as String,
      name: json['name'] as String,
      logoUrl: json['logoUrl'] as String,
      websiteUrl: json['websiteUrl'] as String,
      rating: json['rating'] as double,
      reviewCount: json['reviewCount'] as int,
      isVerified: json['isVerified'] as bool? ?? false,
      isPartner: json['isPartner'] as bool? ?? false,
    );
  }
}

/// A model representing a price point from a specific source
class PricePoint {
  /// Unique identifier for the price point
  final String id;

  /// ID of the travel service
  final String travelServiceId;

  /// Type of travel service
  final TravelServiceType travelServiceType;

  /// Price source
  final PriceSource source;

  /// Base price
  final double basePrice;

  /// Tax amount
  final double taxAmount;

  /// Fee amount
  final double feeAmount;

  /// Discount amount
  final double discountAmount;

  /// Final price
  final double finalPrice;

  /// Currency
  final String currency;

  /// URL to the booking page
  final String bookingUrl;

  /// When the price was retrieved
  final DateTime timestamp;

  /// Whether this price is from cache
  final bool isFromCache;

  /// Additional details about the price
  final Map<String, dynamic> details;

  /// Creates a new price point
  const PricePoint({
    required this.id,
    required this.travelServiceId,
    required this.travelServiceType,
    required this.source,
    required this.basePrice,
    required this.taxAmount,
    required this.feeAmount,
    required this.discountAmount,
    required this.finalPrice,
    required this.currency,
    required this.bookingUrl,
    required this.timestamp,
    this.isFromCache = false,
    this.details = const {},
  });

  /// Creates a copy with some fields replaced
  PricePoint copyWith({
    String? id,
    String? travelServiceId,
    TravelServiceType? travelServiceType,
    PriceSource? source,
    double? basePrice,
    double? taxAmount,
    double? feeAmount,
    double? discountAmount,
    double? finalPrice,
    String? currency,
    String? bookingUrl,
    DateTime? timestamp,
    bool? isFromCache,
    Map<String, dynamic>? details,
  }) {
    return PricePoint(
      id: id ?? this.id,
      travelServiceId: travelServiceId ?? this.travelServiceId,
      travelServiceType: travelServiceType ?? this.travelServiceType,
      source: source ?? this.source,
      basePrice: basePrice ?? this.basePrice,
      taxAmount: taxAmount ?? this.taxAmount,
      feeAmount: feeAmount ?? this.feeAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      finalPrice: finalPrice ?? this.finalPrice,
      currency: currency ?? this.currency,
      bookingUrl: bookingUrl ?? this.bookingUrl,
      timestamp: timestamp ?? this.timestamp,
      isFromCache: isFromCache ?? this.isFromCache,
      details: details ?? this.details,
    );
  }

  /// Get the formatted base price
  String get formattedBasePrice {
    return '$currency${basePrice.toStringAsFixed(2)}';
  }

  /// Get the formatted tax amount
  String get formattedTaxAmount {
    return '$currency${taxAmount.toStringAsFixed(2)}';
  }

  /// Get the formatted fee amount
  String get formattedFeeAmount {
    return '$currency${feeAmount.toStringAsFixed(2)}';
  }

  /// Get the formatted discount amount
  String get formattedDiscountAmount {
    return '$currency${discountAmount.toStringAsFixed(2)}';
  }

  /// Get the formatted final price
  String get formattedFinalPrice {
    return '$currency${finalPrice.toStringAsFixed(2)}';
  }

  /// Get the price breakdown
  Map<String, double> get priceBreakdown {
    return {
      'Base Price': basePrice,
      'Taxes': taxAmount,
      'Fees': feeAmount,
      'Discount': discountAmount,
      'Final Price': finalPrice,
    };
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'travelServiceId': travelServiceId,
      'travelServiceType': travelServiceType.index,
      'source': source.toJson(),
      'basePrice': basePrice,
      'taxAmount': taxAmount,
      'feeAmount': feeAmount,
      'discountAmount': discountAmount,
      'finalPrice': finalPrice,
      'currency': currency,
      'bookingUrl': bookingUrl,
      'timestamp': timestamp.toIso8601String(),
      'isFromCache': isFromCache,
      'details': details,
    };
  }

  /// Creates from JSON
  factory PricePoint.fromJson(Map<String, dynamic> json) {
    return PricePoint(
      id: json['id'] as String,
      travelServiceId: json['travelServiceId'] as String,
      travelServiceType:
          TravelServiceType.values[json['travelServiceType'] as int],
      source: PriceSource.fromJson(json['source'] as Map<String, dynamic>),
      basePrice: json['basePrice'] as double,
      taxAmount: json['taxAmount'] as double,
      feeAmount: json['feeAmount'] as double,
      discountAmount: json['discountAmount'] as double,
      finalPrice: json['finalPrice'] as double,
      currency: json['currency'] as String,
      bookingUrl: json['bookingUrl'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isFromCache: json['isFromCache'] as bool? ?? false,
      details: json['details'] as Map<String, dynamic>? ?? {},
    );
  }
}
