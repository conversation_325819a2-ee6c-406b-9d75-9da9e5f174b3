/// A model representing a transfer driver
class TransferDriver {
  /// Unique identifier for the driver
  final String id;

  /// Name of the driver
  final String name;

  /// Rating of the driver (0-5)
  final double rating;

  /// Number of reviews
  final int reviewCount;

  /// URL to the photo of the driver
  final String photoUrl;

  /// Languages spoken by the driver
  final List<String> languages;

  /// Years of experience
  final int yearsOfExperience;

  /// Whether the driver is verified
  final bool isVerified;

  /// Driver's license number (masked for privacy)
  final String? maskedLicenseNumber;

  /// Driver's phone number (masked for privacy)
  final String? maskedPhoneNumber;

  /// Creates a new transfer driver
  const TransferDriver({
    required this.id,
    required this.name,
    required this.rating,
    required this.reviewCount,
    required this.photoUrl,
    required this.languages,
    required this.yearsOfExperience,
    required this.isVerified,
    this.maskedLicenseNumber,
    this.maskedPhoneNumber,
  });

  /// Create a transfer driver from a JSON map
  factory TransferDriver.fromJson(Map<String, dynamic> json) {
    return TransferDriver(
      id: json['id'] as String,
      name: json['name'] as String,
      rating: json['rating'] as double,
      reviewCount: json['reviewCount'] as int,
      photoUrl: json['photoUrl'] as String,
      languages:
          (json['languages'] as List<dynamic>).map((e) => e as String).toList(),
      yearsOfExperience: json['yearsOfExperience'] as int,
      isVerified: json['isVerified'] as bool,
      maskedLicenseNumber: json['maskedLicenseNumber'] as String?,
      maskedPhoneNumber: json['maskedPhoneNumber'] as String?,
    );
  }

  /// Convert the transfer driver to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'rating': rating,
      'reviewCount': reviewCount,
      'photoUrl': photoUrl,
      'languages': languages,
      'yearsOfExperience': yearsOfExperience,
      'isVerified': isVerified,
      'maskedLicenseNumber': maskedLicenseNumber,
      'maskedPhoneNumber': maskedPhoneNumber,
    };
  }

  /// Get the formatted rating
  String get formattedRating {
    return rating.toStringAsFixed(1);
  }

  /// Get the formatted languages
  String get formattedLanguages {
    if (languages.isEmpty) return 'None';
    return languages.join(', ');
  }

  /// Get the formatted years of experience
  String get formattedYearsOfExperience {
    return '$yearsOfExperience ${yearsOfExperience == 1 ? 'year' : 'years'}';
  }

  /// Create a copy of this transfer driver with the given fields replaced with new values
  TransferDriver copyWith({
    String? id,
    String? name,
    double? rating,
    int? reviewCount,
    String? photoUrl,
    List<String>? languages,
    int? yearsOfExperience,
    bool? isVerified,
    String? maskedLicenseNumber,
    String? maskedPhoneNumber,
  }) {
    return TransferDriver(
      id: id ?? this.id,
      name: name ?? this.name,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      photoUrl: photoUrl ?? this.photoUrl,
      languages: languages ?? this.languages,
      yearsOfExperience: yearsOfExperience ?? this.yearsOfExperience,
      isVerified: isVerified ?? this.isVerified,
      maskedLicenseNumber: maskedLicenseNumber ?? this.maskedLicenseNumber,
      maskedPhoneNumber: maskedPhoneNumber ?? this.maskedPhoneNumber,
    );
  }
}
